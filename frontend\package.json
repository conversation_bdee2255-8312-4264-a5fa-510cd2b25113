{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-types": "tsc -b && vite build", "build:staging": "NODE_ENV=production VITE_APP_ENVIRONMENT=staging vite build", "build:prod": "NODE_ENV=production VITE_APP_ENVIRONMENT=production vite build", "build:analyze": "ANALYZE=true vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "vitest --environment=jsdom", "test:ui": "vitest --ui --environment=jsdom", "test:coverage": "vitest --coverage --environment=jsdom", "test:ci": "vitest run --environment=jsdom --coverage", "deploy:staging": "npm run build:staging && firebase deploy --only hosting:staging", "deploy:prod": "npm run build:prod && npm run test:ci && firebase deploy --only hosting:production", "performance:test": "npm run build && npm run preview & sleep 5 && node -e \"import('./src/scripts/performance-test.ts').then(m => m.runPerformanceTests())\"", "size:check": "npm run build && npx bundlesize", "optimize:css": "node scripts/css-optimization.js", "build:optimized": "npm run build && npm run optimize:css", "analyze": "ANALYZE=true npm run build", "analyze:serve": "npm run analyze && npx serve dist", "test:performance": "vitest run --config vitest.performance.config.ts", "test:lighthouse": "vitest run src/test/performance/lighthouse.test.ts --config vitest.performance.config.ts", "test:load": "vitest run src/test/performance/load.test.ts --config vitest.performance.config.ts", "test:perf:all": "npm run test:performance && npm run test:lighthouse && npm run test:load", "optimize:cdn": "node scripts/cdn-optimization.js", "build:cdn": "npm run build && npm run optimize:cdn", "check:budget": "node scripts/check-performance-budget.js", "build:check": "npm run build && npm run check:budget", "lighthouse": "lhci autorun", "lighthouse:ci": "lhci autorun --upload.target=temporary-public-storage", "lighthouse:server": "lhci server --port=9001"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^24.0.14", "@types/react-window": "^1.8.8", "firebase": "^11.10.0", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^3.1.0", "web-vitals": "^4.2.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@lhci/cli": "^0.15.1", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/puppeteer": "^5.4.7", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "cssnano": "^7.1.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "lighthouse": "^12.8.0", "postcss": "^8.5.6", "puppeteer": "^24.15.0", "purgecss": "^7.0.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-plugin-compression": "^0.5.1", "vitest": "^3.2.4"}}